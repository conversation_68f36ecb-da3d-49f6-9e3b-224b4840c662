import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { AppState } from '@/types';

interface AppStore extends AppState {
  // Actions
  setTheme: (theme: 'light' | 'dark') => void;
  toggleTheme: () => void;
  setLoading: (isLoading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
}

export const useAppStore = create<AppStore>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        theme: 'dark',
        isLoading: false,
        error: null,

        // Actions
        setTheme: (theme) => set({ theme }, false, 'setTheme'),
        toggleTheme: () => {
          const currentTheme = get().theme;
          set({ theme: currentTheme === 'light' ? 'dark' : 'light' }, false, 'toggleTheme');
        },
        setLoading: (isLoading) => set({ isLoading }, false, 'setLoading'),
        setError: (error) => set({ error }, false, 'setError'),
        clearError: () => set({ error: null }, false, 'clearError'),
      }),
      {
        name: 'app-store',
        partialize: (state) => ({ theme: state.theme }),
      }
    ),
    {
      name: 'app-store',
    }
  )
);
