import { useEffect, useCallback } from 'react';

interface KeyboardShortcut {
  key: string;
  ctrlKey?: boolean;
  shiftKey?: boolean;
  altKey?: boolean;
  metaKey?: boolean;
  callback: (event: KeyboardEvent) => void;
  preventDefault?: boolean;
}

/**
 * Custom hook for handling keyboard shortcuts
 * @param shortcuts - Array of keyboard shortcuts to handle
 * @param enabled - Whether the shortcuts are enabled
 */
export function useKeyboard(shortcuts: KeyboardShortcut[], enabled = true) {
  const handleKeyDown = useCallback(
    (event: KeyboardEvent) => {
      if (!enabled) return;

      for (const shortcut of shortcuts) {
        const {
          key,
          ctrlKey = false,
          shiftKey = false,
          altKey = false,
          metaKey = false,
          callback,
          preventDefault = true,
        } = shortcut;

        const keyMatches = event.key.toLowerCase() === key.toLowerCase();
        const ctrlMatches = event.ctrlKey === ctrlKey;
        const shiftMatches = event.shiftKey === shiftKey;
        const altMatches = event.altKey === altKey;
        const metaMatches = event.metaKey === metaKey;

        if (keyMatches && ctrlMatches && shiftMatches && altMatches && metaMatches) {
          if (preventDefault) {
            event.preventDefault();
            event.stopPropagation();
          }
          callback(event);
          break;
        }
      }
    },
    [shortcuts, enabled]
  );

  useEffect(() => {
    if (!enabled) return;

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleKeyDown, enabled]);
}

/**
 * Hook for handling Enter key press
 * @param callback - Function to call when Enter is pressed
 * @param enabled - Whether the handler is enabled
 */
export function useEnterKey(callback: () => void, enabled = true) {
  useKeyboard(
    [
      {
        key: 'Enter',
        callback,
      },
    ],
    enabled
  );
}

/**
 * Hook for handling Escape key press
 * @param callback - Function to call when Escape is pressed
 * @param enabled - Whether the handler is enabled
 */
export function useEscapeKey(callback: () => void, enabled = true) {
  useKeyboard(
    [
      {
        key: 'Escape',
        callback,
      },
    ],
    enabled
  );
}
