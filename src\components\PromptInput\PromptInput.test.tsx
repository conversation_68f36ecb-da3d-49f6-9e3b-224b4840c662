import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import PromptInput from './PromptInput';

// Mock the stores
jest.mock('@/stores/promptStore', () => ({
  usePromptStore: () => ({
    inputState: {
      value: '',
      isSubmitting: false,
      placeholder: 'Type your message...',
      disabled: false,
    },
    setInputValue: jest.fn(),
    setSubmitting: jest.fn(),
    addMessage: jest.fn(),
    clearInput: jest.fn(),
  }),
}));

describe('PromptInput', () => {
  const user = userEvent.setup();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders with default placeholder', () => {
    render(<PromptInput />);
    expect(screen.getByPlaceholderText('Type your message...')).toBeInTheDocument();
  });

  it('renders with custom placeholder', () => {
    render(<PromptInput placeholder="Custom placeholder" />);
    expect(screen.getByPlaceholderText('Custom placeholder')).toBeInTheDocument();
  });

  it('calls onSubmit when submit button is clicked', async () => {
    const mockOnSubmit = jest.fn();
    render(<PromptInput onSubmit={mockOnSubmit} />);
    
    const textarea = screen.getByRole('textbox');
    const submitButton = screen.getByRole('button', { name: /send/i });
    
    await user.type(textarea, 'Test message');
    await user.click(submitButton);
    
    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalledWith('Test message');
    });
  });

  it('calls onSubmit when Ctrl+Enter is pressed', async () => {
    const mockOnSubmit = jest.fn();
    render(<PromptInput onSubmit={mockOnSubmit} />);
    
    const textarea = screen.getByRole('textbox');
    
    await user.type(textarea, 'Test message');
    await user.keyboard('{Control>}{Enter}{/Control}');
    
    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalledWith('Test message');
    });
  });

  it('disables submit button when input is empty', () => {
    render(<PromptInput />);
    
    const submitButton = screen.getByRole('button', { name: /send/i });
    expect(submitButton).toBeDisabled();
  });

  it('enables submit button when input has content', async () => {
    render(<PromptInput />);
    
    const textarea = screen.getByRole('textbox');
    const submitButton = screen.getByRole('button', { name: /send/i });
    
    await user.type(textarea, 'Test message');
    
    expect(submitButton).not.toBeDisabled();
  });

  it('shows character count when near limit', async () => {
    render(<PromptInput maxLength={100} />);
    
    const textarea = screen.getByRole('textbox');
    
    // Type 85 characters (over 80% of 100)
    await user.type(textarea, 'a'.repeat(85));
    
    expect(screen.getByText('85/100')).toBeInTheDocument();
  });

  it('prevents input when over character limit', async () => {
    render(<PromptInput maxLength={10} />);
    
    const textarea = screen.getByRole('textbox');
    
    await user.type(textarea, 'This is a very long message that exceeds the limit');
    
    // Should only contain first 10 characters
    expect(textarea).toHaveValue('This is a ');
  });

  it('calls onChange when controlled', async () => {
    const mockOnChange = jest.fn();
    render(<PromptInput value="initial" onChange={mockOnChange} />);
    
    const textarea = screen.getByRole('textbox');
    
    await user.type(textarea, ' more text');
    
    expect(mockOnChange).toHaveBeenCalled();
  });

  it('shows helper text', () => {
    render(<PromptInput multiline />);
    
    expect(screen.getByText('Press Ctrl+Enter to send')).toBeInTheDocument();
  });

  it('shows different helper text for single line', () => {
    render(<PromptInput multiline={false} />);
    
    expect(screen.getByText('Press Enter to send')).toBeInTheDocument();
  });
});
