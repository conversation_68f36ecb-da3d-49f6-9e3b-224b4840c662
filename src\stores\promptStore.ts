import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { PromptMessage, PromptSession, PromptInputState } from '@/types';

interface PromptStore {
  // State
  currentSession: PromptSession | null;
  sessions: PromptSession[];
  inputState: PromptInputState;
  
  // Actions
  createSession: (title?: string) => void;
  addMessage: (content: string, type: 'user' | 'assistant') => void;
  updateInputState: (updates: Partial<PromptInputState>) => void;
  setInputValue: (value: string) => void;
  clearInput: () => void;
  setSubmitting: (isSubmitting: boolean) => void;
  loadSessions: () => void;
  selectSession: (sessionId: string) => void;
  deleteSession: (sessionId: string) => void;
}

const generateId = () => Math.random().toString(36).substr(2, 9);

export const usePromptStore = create<PromptStore>()(
  devtools(
    (set, get) => ({
      // Initial state
      currentSession: null,
      sessions: [],
      inputState: {
        value: '',
        isSubmitting: false,
        placeholder: 'Type your message...',
        disabled: false,
      },

      // Actions
      createSession: (title = 'New Session') => {
        const newSession: PromptSession = {
          id: generateId(),
          title,
          messages: [],
          createdAt: new Date(),
          updatedAt: new Date(),
        };
        
        set((state) => ({
          currentSession: newSession,
          sessions: [newSession, ...state.sessions],
        }), false, 'createSession');
      },

      addMessage: (content, type) => {
        const { currentSession } = get();
        if (!currentSession) return;

        const newMessage: PromptMessage = {
          id: generateId(),
          content,
          type,
          timestamp: new Date(),
        };

        const updatedSession = {
          ...currentSession,
          messages: [...currentSession.messages, newMessage],
          updatedAt: new Date(),
        };

        set((state) => ({
          currentSession: updatedSession,
          sessions: state.sessions.map(session => 
            session.id === currentSession.id ? updatedSession : session
          ),
        }), false, 'addMessage');
      },

      updateInputState: (updates) => {
        set((state) => ({
          inputState: { ...state.inputState, ...updates },
        }), false, 'updateInputState');
      },

      setInputValue: (value) => {
        set((state) => ({
          inputState: { ...state.inputState, value },
        }), false, 'setInputValue');
      },

      clearInput: () => {
        set((state) => ({
          inputState: { ...state.inputState, value: '' },
        }), false, 'clearInput');
      },

      setSubmitting: (isSubmitting) => {
        set((state) => ({
          inputState: { ...state.inputState, isSubmitting },
        }), false, 'setSubmitting');
      },

      loadSessions: () => {
        // TODO: Load sessions from database
        console.log('Loading sessions from database...');
      },

      selectSession: (sessionId) => {
        const { sessions } = get();
        const session = sessions.find(s => s.id === sessionId);
        if (session) {
          set({ currentSession: session }, false, 'selectSession');
        }
      },

      deleteSession: (sessionId) => {
        set((state) => {
          const updatedSessions = state.sessions.filter(s => s.id !== sessionId);
          const currentSession = state.currentSession?.id === sessionId 
            ? (updatedSessions[0] || null) 
            : state.currentSession;
          
          return {
            sessions: updatedSessions,
            currentSession,
          };
        }, false, 'deleteSession');
      },
    }),
    {
      name: 'prompt-store',
    }
  )
);
