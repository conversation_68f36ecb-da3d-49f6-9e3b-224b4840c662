import { useEffect, useRef } from 'react';

/**
 * Custom hook for auto-resizing textarea elements
 * @param value - The current value of the textarea
 * @param minRows - Minimum number of rows
 * @param maxRows - Maximum number of rows
 * @returns Ref to attach to the textarea element
 */
export function useAutoResize(value: string, minRows = 1, maxRows = 10) {
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  useEffect(() => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    // Reset height to auto to get the correct scrollHeight
    textarea.style.height = 'auto';
    
    // Calculate the line height
    const computedStyle = window.getComputedStyle(textarea);
    const lineHeight = parseInt(computedStyle.lineHeight, 10) || 20;
    
    // Calculate min and max heights
    const minHeight = lineHeight * minRows;
    const maxHeight = lineHeight * maxRows;
    
    // Set the height based on scroll height, but within min/max bounds
    const scrollHeight = textarea.scrollHeight;
    const newHeight = Math.min(Math.max(scrollHeight, minHeight), maxHeight);
    
    textarea.style.height = `${newHeight}px`;
    
    // Show/hide scrollbar based on whether content exceeds max height
    textarea.style.overflowY = scrollHeight > maxHeight ? 'auto' : 'hidden';
  }, [value, minRows, maxRows]);

  return textareaRef;
}
