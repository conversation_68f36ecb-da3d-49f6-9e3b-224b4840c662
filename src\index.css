/* CSS Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  overflow: hidden;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON><PERSON>, sans-serif;
  background-color: #1a1a1a;
  color: #e0e0e0;
  line-height: 1.6;
  font-size: 14px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* App Container */
#app {
  height: 100vh;
  width: 100vw;
  background-color: #1a1a1a;
}



/* Scrollbar Styling for Dark Theme */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #2d2d2d;
}

::-webkit-scrollbar-thumb {
  background: #555555;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #666666;
}

/* Selection Styling */
::selection {
  background-color: #4a9eff;
  color: #ffffff;
}

::-moz-selection {
  background-color: #4a9eff;
  color: #ffffff;
}
