import React from 'react';
import { createRoot } from 'react-dom/client';
import App from './components/App';
import './index.css';

// Prevent default drag and drop behavior
document.addEventListener('dragover', (e) => {
  e.preventDefault();
});

document.addEventListener('drop', (e) => {
  e.preventDefault();
});

// Initialize React app
const container = document.getElementById('app');
if (container) {
  const root = createRoot(container);
  root.render(<App />);
} else {
  console.error('Could not find app container element');
}
