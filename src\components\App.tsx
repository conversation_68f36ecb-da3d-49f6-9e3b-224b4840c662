import React, { useEffect } from 'react';
import { useTheme } from '@/hooks';
import { usePromptStore } from '@/stores/promptStore';
import PromptInput from '@/components/PromptInput';
import { cn } from '@/utils';

// Theme toggle button component
const ThemeToggle: React.FC = () => {
  const { theme, toggleTheme } = useTheme();
  
  return (
    <button
      onClick={toggleTheme}
      className={cn(
        'fixed top-4 right-4 p-2 rounded-lg transition-all duration-200',
        'bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700',
        'hover:bg-gray-50 dark:hover:bg-gray-700',
        'focus:outline-none focus:ring-2 focus:ring-primary-500/20',
        'shadow-sm hover:shadow-md'
      )}
      title={`Switch to ${theme === 'light' ? 'dark' : 'light'} mode`}
    >
      {theme === 'light' ? (
        // Moon icon for dark mode
        <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
        </svg>
      ) : (
        // Sun icon for light mode
        <svg className="w-5 h-5 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
        </svg>
      )}
    </button>
  );
};

// Welcome message component
const WelcomeMessage: React.FC = () => {
  return (
    <div className="text-center max-w-2xl mx-auto mb-8">
      <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
        Welcome to Arien Agent
      </h1>
      <p className="text-lg text-gray-600 dark:text-gray-300 mb-6">
        Your intelligent AI assistant for productive conversations and creative collaboration.
      </p>
      <div className="flex flex-wrap justify-center gap-2 text-sm text-gray-500 dark:text-gray-400">
        <span className="px-3 py-1 bg-gray-100 dark:bg-gray-800 rounded-full">
          💬 Natural conversations
        </span>
        <span className="px-3 py-1 bg-gray-100 dark:bg-gray-800 rounded-full">
          🚀 Fast responses
        </span>
        <span className="px-3 py-1 bg-gray-100 dark:bg-gray-800 rounded-full">
          🎯 Focused assistance
        </span>
      </div>
    </div>
  );
};

// Message display component
const MessageList: React.FC = () => {
  const { currentSession } = usePromptStore();
  
  if (!currentSession || currentSession.messages.length === 0) {
    return <WelcomeMessage />;
  }

  return (
    <div className="flex-1 overflow-y-auto px-4 py-6 space-y-4">
      {currentSession.messages.map((message) => (
        <div
          key={message.id}
          className={cn(
            'flex',
            message.type === 'user' ? 'justify-end' : 'justify-start'
          )}
        >
          <div
            className={cn(
              'max-w-[80%] px-4 py-3 rounded-2xl',
              message.type === 'user'
                ? 'bg-primary-600 text-white'
                : 'bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100'
            )}
          >
            <p className="text-sm leading-relaxed whitespace-pre-wrap">
              {message.content}
            </p>
            <div className="mt-2 text-xs opacity-70">
              {new Date(message.timestamp).toLocaleTimeString()}
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

// Main App component
const App: React.FC = () => {
  const { createSession, currentSession } = usePromptStore();

  // Initialize the app
  useEffect(() => {
    // Create initial session if none exists
    if (!currentSession) {
      createSession('Welcome Session');
    }
  }, [createSession, currentSession]);

  // Handle prompt submission
  const handlePromptSubmit = async (value: string) => {
    console.log('Prompt submitted:', value);
    
    // Simulate AI response (replace with actual AI integration)
    setTimeout(() => {
      const responses = [
        "I understand your request. How can I help you further?",
        "That's an interesting question. Let me think about that...",
        "I'd be happy to assist you with that. Here's what I think:",
        "Great question! Based on what you've shared, I would suggest:",
        "I see what you're looking for. Let me provide some insights:",
      ];
      
      const randomResponse = responses[Math.floor(Math.random() * responses.length)];
      // This would be replaced with actual AI response logic
      console.log('AI Response:', randomResponse);
    }, 1000);
  };

  return (
    <div className="h-screen flex flex-col bg-gray-50 dark:bg-gray-900 transition-colors duration-200">
      {/* Theme toggle */}
      <ThemeToggle />
      
      {/* Main content area */}
      <div className="flex-1 flex flex-col max-w-6xl mx-auto w-full">
        {/* Messages area */}
        <MessageList />
        
        {/* Input area */}
        <div className="flex-shrink-0 p-4 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
          <PromptInput
            onSubmit={handlePromptSubmit}
            placeholder="Type your message here..."
            autoFocus
            className="w-full"
          />
        </div>
      </div>
    </div>
  );
};

export default App;
