// Core application types
export interface AppState {
  theme: 'light' | 'dark';
  isLoading: boolean;
  error: string | null;
}

// Prompt related types
export interface PromptMessage {
  id: string;
  content: string;
  timestamp: Date;
  type: 'user' | 'assistant';
  metadata?: Record<string, any>;
}

export interface PromptSession {
  id: string;
  title: string;
  messages: PromptMessage[];
  createdAt: Date;
  updatedAt: Date;
}

export interface PromptInputState {
  value: string;
  isSubmitting: boolean;
  placeholder: string;
  maxLength?: number;
  disabled?: boolean;
}

// Database types
export interface DatabaseConfig {
  path: string;
  options?: {
    verbose?: boolean;
    fileMustExist?: boolean;
  };
}

// Component props types
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
}

export interface PromptInputProps extends BaseComponentProps {
  value?: string;
  placeholder?: string;
  onSubmit?: (value: string) => void;
  onChange?: (value: string) => void;
  disabled?: boolean;
  maxLength?: number;
  autoFocus?: boolean;
  multiline?: boolean;
  rows?: number;
}

// Utility types
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;
