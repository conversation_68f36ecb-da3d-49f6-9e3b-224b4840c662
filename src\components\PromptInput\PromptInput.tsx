import React, { useState, useCallback, useRef, useEffect } from 'react';
import { cn } from '@/utils';
import { usePromptStore } from '@/stores/promptStore';
import { useAutoResize, useKeyboard } from '@/hooks';
import { PromptInputProps } from '@/types';
import Button from '@/components/ui/Button';

// Icons as SVG components
const SendIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
  </svg>
);

const AttachIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
  </svg>
);

const MicIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
  </svg>
);

const PromptInput: React.FC<PromptInputProps> = ({
  value: controlledValue,
  placeholder = 'Type your message...',
  onSubmit,
  onChange,
  disabled = false,
  maxLength = 4000,
  autoFocus = false,
  multiline = true,
  rows = 1,
  className,
  ...props
}) => {
  // Store integration
  const { inputState, setInputValue, setSubmitting, addMessage, clearInput } = usePromptStore();
  
  // Local state for controlled vs uncontrolled behavior
  const [localValue, setLocalValue] = useState('');
  const isControlled = controlledValue !== undefined;
  const currentValue = isControlled ? controlledValue : (inputState.value || localValue);
  
  // Refs
  const textareaRef = useAutoResize(currentValue, rows, 8);
  const containerRef = useRef<HTMLDivElement>(null);
  
  // Character count
  const characterCount = currentValue.length;
  const isNearLimit = characterCount > maxLength * 0.8;
  const isOverLimit = characterCount > maxLength;
  
  // Determine if submit should be disabled
  const canSubmit = currentValue.trim().length > 0 && !isOverLimit && !disabled && !inputState.isSubmitting;

  // Handle value changes
  const handleValueChange = useCallback((newValue: string) => {
    if (isControlled) {
      onChange?.(newValue);
    } else {
      setLocalValue(newValue);
      setInputValue(newValue);
    }
  }, [isControlled, onChange, setInputValue]);

  // Handle input change
  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    if (newValue.length <= maxLength) {
      handleValueChange(newValue);
    }
  }, [handleValueChange, maxLength]);

  // Handle submit
  const handleSubmit = useCallback(async () => {
    if (!canSubmit) return;
    
    const valueToSubmit = currentValue.trim();
    if (!valueToSubmit) return;

    try {
      setSubmitting(true);
      
      // Call external onSubmit if provided
      if (onSubmit) {
        await onSubmit(valueToSubmit);
      } else {
        // Default behavior: add to store
        addMessage(valueToSubmit, 'user');
      }
      
      // Clear input after successful submit
      if (isControlled) {
        onChange?.('');
      } else {
        setLocalValue('');
        clearInput();
      }
    } catch (error) {
      console.error('Error submitting prompt:', error);
    } finally {
      setSubmitting(false);
    }
  }, [canSubmit, currentValue, onSubmit, setSubmitting, addMessage, isControlled, onChange, clearInput]);

  // Keyboard shortcuts
  useKeyboard([
    {
      key: 'Enter',
      ctrlKey: true,
      callback: handleSubmit,
      preventDefault: true,
    },
    {
      key: 'Enter',
      shiftKey: false,
      callback: (e) => {
        if (!multiline) {
          e.preventDefault();
          handleSubmit();
        }
      },
      preventDefault: false,
    },
  ]);

  // Auto-focus
  useEffect(() => {
    if (autoFocus && textareaRef.current) {
      textareaRef.current.focus();
    }
  }, [autoFocus]);

  return (
    <div 
      ref={containerRef}
      className={cn(
        'relative w-full max-w-4xl mx-auto',
        className
      )}
      {...props}
    >
      {/* Main input container */}
      <div className={cn(
        'relative flex items-end gap-2 p-3 rounded-2xl border transition-all duration-200',
        'bg-white dark:bg-gray-800',
        'border-gray-200 dark:border-gray-700',
        'focus-within:border-primary-500 dark:focus-within:border-primary-400',
        'focus-within:ring-2 focus-within:ring-primary-500/20',
        'shadow-sm hover:shadow-md focus-within:shadow-lg',
        disabled && 'opacity-50 cursor-not-allowed',
        isOverLimit && 'border-red-500 dark:border-red-400 focus-within:border-red-500 focus-within:ring-red-500/20'
      )}>
        
        {/* Left actions */}
        <div className="flex items-center gap-1 pb-2">
          <button
            type="button"
            className={cn(
              'p-2 rounded-lg transition-colors duration-200',
              'text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300',
              'hover:bg-gray-100 dark:hover:bg-gray-700',
              'focus:outline-none focus:ring-2 focus:ring-primary-500/20',
              disabled && 'cursor-not-allowed opacity-50'
            )}
            disabled={disabled}
            title="Attach file"
          >
            <AttachIcon />
          </button>
          
          <button
            type="button"
            className={cn(
              'p-2 rounded-lg transition-colors duration-200',
              'text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300',
              'hover:bg-gray-100 dark:hover:bg-gray-700',
              'focus:outline-none focus:ring-2 focus:ring-primary-500/20',
              disabled && 'cursor-not-allowed opacity-50'
            )}
            disabled={disabled}
            title="Voice input"
          >
            <MicIcon />
          </button>
        </div>

        {/* Text input */}
        <div className="flex-1 min-w-0">
          <textarea
            ref={textareaRef}
            value={currentValue}
            onChange={handleInputChange}
            placeholder={placeholder}
            disabled={disabled || inputState.isSubmitting}
            className={cn(
              'w-full resize-none border-0 bg-transparent p-0 text-gray-900 dark:text-gray-100',
              'placeholder:text-gray-400 dark:placeholder:text-gray-500',
              'focus:outline-none focus:ring-0',
              'text-sm leading-6',
              'min-h-[24px] max-h-[200px]',
              'scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600',
              disabled && 'cursor-not-allowed'
            )}
            rows={rows}
            style={{ 
              height: 'auto',
              overflowY: 'hidden'
            }}
          />
        </div>

        {/* Right actions */}
        <div className="flex items-end gap-2 pb-2">
          {/* Character count */}
          {(isNearLimit || isOverLimit) && (
            <div className={cn(
              'text-xs font-medium px-2 py-1 rounded-md',
              isOverLimit 
                ? 'text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20' 
                : 'text-amber-600 dark:text-amber-400 bg-amber-50 dark:bg-amber-900/20'
            )}>
              {characterCount}/{maxLength}
            </div>
          )}
          
          {/* Submit button */}
          <Button
            onClick={handleSubmit}
            disabled={!canSubmit}
            loading={inputState.isSubmitting}
            size="sm"
            className={cn(
              'rounded-xl px-3 py-2 min-w-[44px]',
              canSubmit 
                ? 'bg-primary-600 hover:bg-primary-700 text-white' 
                : 'bg-gray-200 dark:bg-gray-700 text-gray-400 dark:text-gray-500 cursor-not-allowed'
            )}
            title={multiline ? 'Send (Ctrl+Enter)' : 'Send (Enter)'}
          >
            <SendIcon />
          </Button>
        </div>
      </div>

      {/* Helper text */}
      <div className="flex items-center justify-between mt-2 px-1">
        <div className="text-xs text-gray-500 dark:text-gray-400">
          {multiline ? 'Press Ctrl+Enter to send' : 'Press Enter to send'}
        </div>
        
        {!isNearLimit && !isOverLimit && (
          <div className="text-xs text-gray-400 dark:text-gray-500">
            {characterCount > 0 && `${characterCount} characters`}
          </div>
        )}
      </div>
    </div>
  );
};

export default PromptInput;
