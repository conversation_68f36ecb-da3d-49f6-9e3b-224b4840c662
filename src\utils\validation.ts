// Validation utilities

/**
 * Validate email format
 */
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Validate URL format
 */
export const isValidUrl = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

/**
 * Validate that a string is not empty or just whitespace
 */
export const isNotEmpty = (value: string): boolean => {
  return value.trim().length > 0;
};

/**
 * Validate string length
 */
export const isValidLength = (value: string, min = 0, max = Infinity): boolean => {
  const length = value.length;
  return length >= min && length <= max;
};

/**
 * Validate that a value is a number
 */
export const isNumber = (value: any): boolean => {
  return !isNaN(Number(value)) && isFinite(Number(value));
};

/**
 * Validate that a number is within a range
 */
export const isInRange = (value: number, min = -Infinity, max = Infinity): boolean => {
  return value >= min && value <= max;
};

/**
 * Validate that a string contains only alphanumeric characters
 */
export const isAlphanumeric = (value: string): boolean => {
  const alphanumericRegex = /^[a-zA-Z0-9]+$/;
  return alphanumericRegex.test(value);
};

/**
 * Validate password strength
 */
export const validatePassword = (password: string): {
  isValid: boolean;
  errors: string[];
} => {
  const errors: string[] = [];
  
  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long');
  }
  
  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }
  
  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }
  
  if (!/\d/.test(password)) {
    errors.push('Password must contain at least one number');
  }
  
  if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    errors.push('Password must contain at least one special character');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
};

/**
 * Generic validation function
 */
export const validate = (
  value: any,
  rules: Array<(value: any) => boolean | string>
): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];
  
  for (const rule of rules) {
    const result = rule(value);
    if (result !== true) {
      errors.push(typeof result === 'string' ? result : 'Validation failed');
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
};
