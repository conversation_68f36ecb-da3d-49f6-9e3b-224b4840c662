import Database from 'better-sqlite3';
import { PromptSession, PromptMessage, DatabaseConfig } from '@/types';

class DatabaseManager {
  private db: Database.Database | null = null;
  private config: DatabaseConfig;

  constructor(config: DatabaseConfig) {
    this.config = config;
  }

  initialize(): void {
    try {
      this.db = new Database(this.config.path, this.config.options);
      this.createTables();
      console.log('Database initialized successfully');
    } catch (error) {
      console.error('Failed to initialize database:', error);
      throw error;
    }
  }

  private createTables(): void {
    if (!this.db) throw new Error('Database not initialized');

    // Create sessions table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS sessions (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Create messages table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS messages (
        id TEXT PRIMARY KEY,
        session_id TEXT NOT NULL,
        content TEXT NOT NULL,
        type TEXT NOT NULL CHECK (type IN ('user', 'assistant')),
        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
        metadata TEXT,
        FOREIGN KEY (session_id) REFERENCES sessions (id) ON DELETE CASCADE
      )
    `);

    // Create indexes for better performance
    this.db.exec(`
      CREATE INDEX IF NOT EXISTS idx_messages_session_id ON messages (session_id);
      CREATE INDEX IF NOT EXISTS idx_messages_timestamp ON messages (timestamp);
      CREATE INDEX IF NOT EXISTS idx_sessions_updated_at ON sessions (updated_at);
    `);
  }

  // Session operations
  createSession(session: Omit<PromptSession, 'messages'>): void {
    if (!this.db) throw new Error('Database not initialized');

    const stmt = this.db.prepare(`
      INSERT INTO sessions (id, title, created_at, updated_at)
      VALUES (?, ?, ?, ?)
    `);

    stmt.run(
      session.id,
      session.title,
      session.createdAt.toISOString(),
      session.updatedAt.toISOString()
    );
  }

  getSessions(): PromptSession[] {
    if (!this.db) throw new Error('Database not initialized');

    const sessionsStmt = this.db.prepare(`
      SELECT * FROM sessions ORDER BY updated_at DESC
    `);

    const messagesStmt = this.db.prepare(`
      SELECT * FROM messages WHERE session_id = ? ORDER BY timestamp ASC
    `);

    const sessions = sessionsStmt.all() as any[];
    
    return sessions.map(session => ({
      id: session.id,
      title: session.title,
      createdAt: new Date(session.created_at),
      updatedAt: new Date(session.updated_at),
      messages: messagesStmt.all(session.id).map((msg: any) => ({
        id: msg.id,
        content: msg.content,
        type: msg.type,
        timestamp: new Date(msg.timestamp),
        metadata: msg.metadata ? JSON.parse(msg.metadata) : undefined,
      })),
    }));
  }

  getSession(sessionId: string): PromptSession | null {
    if (!this.db) throw new Error('Database not initialized');

    const sessionStmt = this.db.prepare(`
      SELECT * FROM sessions WHERE id = ?
    `);

    const messagesStmt = this.db.prepare(`
      SELECT * FROM messages WHERE session_id = ? ORDER BY timestamp ASC
    `);

    const session = sessionStmt.get(sessionId) as any;
    if (!session) return null;

    const messages = messagesStmt.all(sessionId) as any[];

    return {
      id: session.id,
      title: session.title,
      createdAt: new Date(session.created_at),
      updatedAt: new Date(session.updated_at),
      messages: messages.map(msg => ({
        id: msg.id,
        content: msg.content,
        type: msg.type,
        timestamp: new Date(msg.timestamp),
        metadata: msg.metadata ? JSON.parse(msg.metadata) : undefined,
      })),
    };
  }

  updateSession(sessionId: string, updates: Partial<Pick<PromptSession, 'title'>>): void {
    if (!this.db) throw new Error('Database not initialized');

    const stmt = this.db.prepare(`
      UPDATE sessions 
      SET title = COALESCE(?, title), updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `);

    stmt.run(updates.title, sessionId);
  }

  deleteSession(sessionId: string): void {
    if (!this.db) throw new Error('Database not initialized');

    const stmt = this.db.prepare(`DELETE FROM sessions WHERE id = ?`);
    stmt.run(sessionId);
  }

  // Message operations
  addMessage(sessionId: string, message: PromptMessage): void {
    if (!this.db) throw new Error('Database not initialized');

    const transaction = this.db.transaction(() => {
      // Insert message
      const messageStmt = this.db!.prepare(`
        INSERT INTO messages (id, session_id, content, type, timestamp, metadata)
        VALUES (?, ?, ?, ?, ?, ?)
      `);

      messageStmt.run(
        message.id,
        sessionId,
        message.content,
        message.type,
        message.timestamp.toISOString(),
        message.metadata ? JSON.stringify(message.metadata) : null
      );

      // Update session timestamp
      const sessionStmt = this.db!.prepare(`
        UPDATE sessions SET updated_at = CURRENT_TIMESTAMP WHERE id = ?
      `);
      sessionStmt.run(sessionId);
    });

    transaction();
  }

  close(): void {
    if (this.db) {
      this.db.close();
      this.db = null;
    }
  }
}

// Export singleton instance
export const databaseManager = new DatabaseManager({
  path: './data/app.db',
  options: {
    verbose: process.env.NODE_ENV === 'development' ? console.log : undefined,
  },
});

export default DatabaseManager;
